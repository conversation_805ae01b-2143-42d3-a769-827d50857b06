import {errorType} from '../configurations/errorType';
import {ScraperConfiguration} from '../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {BinaryProxy, LoginParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {BinaryLoginResult} from '../processes/types/resultTypes';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {LoginRunContext} from './jobs/context';
import {LoginJob} from './jobs/LoginJob';
import {SaveConfigurationJob} from './jobs/SaveConfigurationJob';
import {SyncDependenciesJob} from './jobs/SyncDependenciesJob';
import {Run} from './Run';

export class LoginWithCredentialsRun extends Run<LoginRunContext, BinaryLoginResult> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            loginParams: LoginParams;
            s2Client: iScraperServiceClient;
            scraperConfigurationManager: ScraperConfigurationManager;
        },
        options: {
            context?: Omit<LoginRunContext, 'operationId' | 'isShadowRun'>;
        }
    ) {
        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                new LoginJob(dependencies.binaryProxy, dependencies.loginParams),
                new SaveConfigurationJob(dependencies.scraperConfigurationManager, dependencies.loginParams.credentials)
            ],
            options
        );

        this.setEventHandlers({
            onScheduled: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'SCHEDULED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onStart: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STARTED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onSuccess: async () => {
                telemetry.trace(`Login successful for ${this.context.source}`);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'CONFIGURED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onFail: async (e: any) => {
                telemetry.trace(`Login failed for ${this.context.source}`);
                telemetry.exception(e);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'FAILED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined,
                    reason: e?.errorType ?? errorType.UNEXPECTED_ERROR
                });
            },
            onFinally: async () => {
                if (this.context.isShadowRun && dependencies.loginParams?.sessionPath) {
                    await removeFileOrDirectory(dependencies.loginParams.sessionPath, false);
                }
            },
            onKill: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STOPPED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            }
        });
    }

    async getResult(): Promise<ScraperConfiguration[]> {
        const results = await this.getJobResults();
        return results[results.length - 1] as any as ScraperConfiguration[];
    }
}
