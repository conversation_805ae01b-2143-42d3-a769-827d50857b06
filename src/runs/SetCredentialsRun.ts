import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../configurations/SourceAccount';
import {BinaryProxy, LoginParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {BinaryLoginResult} from '../processes/types/resultTypes';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {LoginJob, LoginJobContext} from './jobs/LoginJob';
import {SyncDependenciesJob, SyncDependenciesJobContext} from './jobs/SyncDependenciesJob';
import {UpdateSourceAccountJob, UpdateSourceAccountJobContext} from './jobs/UpdateSourceAccountJob';
import {Run} from './Run';

export type SetCredentialsRunContext = SyncDependenciesJobContext & LoginJobContext & UpdateSourceAccountJobContext;

export class SetCredentialsRun extends Run<SetCredentialsRunContext, BinaryLoginResult> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            loginParams: LoginParams;
            s2Client: iScraperServiceClient;
            scraperConfigurationManager: ScraperConfigurationManager;
        },
        options: {
            context?: Omit<SetCredentialsRunContext, 'operationId' | 'isShadowRun'>;
            sourceAccountId: string;
        }
    ) {
        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                new LoginJob(dependencies.binaryProxy, dependencies.loginParams),
                new UpdateSourceAccountJob(dependencies.scraperConfigurationManager, dependencies.loginParams, options.sourceAccountId)
            ],
            options
        );
    }

    async getResult(): Promise<SourceAccount> {
        const results = await this.getJobResults();
        return results[results.length - 1] as any as SourceAccount;
    }
}
