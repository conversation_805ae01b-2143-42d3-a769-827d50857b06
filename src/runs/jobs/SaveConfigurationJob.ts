import {errorType} from '../../configurations/errorType';
import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../../configurations/ScraperConfigurationManager';
import {CheckSessionResult} from '../../processes/types';
import {ScraperLibError} from '../../processes/types/errors';
import {Credentials, credentialsToSourceAccount} from '../credentials';
import {BaseContext} from './context';
import {Job} from './Job';

export interface SaveConfigurationJobContext extends BaseContext {
    readonly sessionPath: string;
    readonly checkSessionResult?: CheckSessionResult;
}

export class SaveConfigurationJob extends Job<ScraperConfiguration[], SaveConfigurationJobContext> {
    constructor(private readonly scraperConfigurationManager: ScraperConfigurationManager, private credentials?: Credentials) {
        super();
    }

    async execute(context: SaveConfigurationJobContext): Promise<ScraperConfiguration[]> {
        if (!context.checkSessionResult || !context.checkSessionResult.id) {
            throw new ScraperLibError(errorType.INTERNAL_SCRAPER_LIB_ERROR, 'error', {
                message: 'CheckSessionResult is not defined and a configuration is trying to be saved',
                context
            });
        }

        const sourceAccountCredentials = this.credentials ? credentialsToSourceAccount(context.source, this.credentials as any) : {};

        return await this.scraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs(context.source, {
            ...sourceAccountCredentials,
            sessionPath: context.sessionPath,
            accountIdentifier: context.checkSessionResult!.id
        });
    }
}
