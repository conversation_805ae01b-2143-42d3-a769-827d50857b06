import {DependenciesManager} from '../../dependencies/DependenciesManager';
import {BaseContext} from './context';
import {Job} from './Job';

export type SyncDependenciesJobContext = BaseContext;
export class SyncDependenciesJob extends Job<void> {
    constructor(private dependenciesManager: DependenciesManager) {
        super();
    }

    async execute(): Promise<void> {
        await this.dependenciesManager.syncDependencies(false, true);
    }
}
