import {BinaryProxy, LoginParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {BinaryLoginResult} from '../processes/types/resultTypes';
import {DeleteSessionJob, DeleteSessionJobContext} from './jobs/DeleteSessionJob';
import {LoginJob, LoginJobContext} from './jobs/LoginJob';
import {SyncDependenciesJob, SyncDependenciesJobContext} from './jobs/SyncDependenciesJob';
import {Run} from './Run';

export type CheckCredentialsRunContext = SyncDependenciesJobContext & LoginJobContext & DeleteSessionJobContext;

export class CheckCredentialsRun extends Run<CheckCredentialsRunContext, BinaryLoginResult> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            loginParams: LoginParams;
        },
        options: {
            context?: Omit<CheckCredentialsRunContext, 'operationId' | 'isShadowRun'>;
        }
    ) {
        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                // Login & Create Session
                new LoginJob(dependencies.binaryProxy, dependencies.loginParams),
                // Delete Session
                new DeleteSessionJob()
            ],
            options
        );
    }

    async getResult(): Promise<BinaryLoginResult> {
        const [_, loginJobResult] = await this.getJobResults();
        return loginJobResult;
    }
}
