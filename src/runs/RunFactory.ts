import {ScraperConfiguration} from '../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../configurations/SourceAccount';
import {BinaryProxy, CheckSessionParams, CommonParams, LoginParams, ScrapeParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {getFeatureFlags} from '../featureFlags/featureFlags';
import {BinaryLoginResult, ManualLoginDetailsResult, SourceSideOrganization} from '../processes/types';
import {Storage} from '../storage/Storage';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {TriggeredBy} from '../telemetry/scraperService/scraperServiceEvents';
import {Command, DateRange, ShadowModeTask, Source} from '../types';
import {sumDaysInDateRanges} from '../utils/datesUtils';
import {buildSessionPath, copySessionFileForShadowRun} from '../utils/session';
import {CheckCredentialsRun} from './CheckCredentialsRun';
import {Credentials, sourceAccountToCredentials, sourceAccountToScrapeCredentials} from './credentials';
import {BaseContext, LoginWithCookiesRunContext, ScrapeRunContext} from './jobs/context';
import {GetManualLoginDataJob} from './jobs/GetManualLoginDataJob';
import {GetSourceSideOrganizationsJob} from './jobs/GetSourceSideOrganizationsJob';
import {LoginWithCookiesRun} from './LoginWithCookiesRun';
import {LoginWithCredentialsRun} from './LoginWithCredentialsRun';
import {getSourcePriority} from './PriorityQueue';
import {Run} from './Run';
import {ScrapeRun} from './ScrapeRun';
import {SetCredentialsRun} from './SetCredentialsRun';
import {getShadowModeStatusHelpers, getStatusHelpers} from './status';

export class RunFactory {
    constructor(
        private readonly mainDir: string,
        private readonly storage: Storage,
        private readonly dependenciesManager: DependenciesManager,
        private readonly binaryProxy: BinaryProxy,
        private readonly s2Client: iScraperServiceClient,
        private readonly scraperConfigurationManager: ScraperConfigurationManager
    ) {}

    async createScrapeRun(
        source: Source,
        dateRanges: DateRange[],
        scraperConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy,
        shadowModeTask?: ShadowModeTask
    ): Promise<Run<ScrapeRunContext>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask);

        const scrapeParams: Omit<ScrapeParams, 'dateFrom' | 'dateTo'> = {
            ...commonParams,
            skuToIgnore: scraperConfiguration.skuToIgnore,
            credentials: sourceAccountToScrapeCredentials(source, sourceAccount)
        };

        const loginParams: LoginParams = {
            ...commonParams,
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        const checkSessionParams: CheckSessionParams = {...commonParams};

        const {updateStatus, updateLastScrapeDate} = shadowModeTask ? getShadowModeStatusHelpers(shadowModeTask) : getStatusHelpers(this.storage, scraperConfiguration);

        return new ScrapeRun(
            {
                mainDir: this.mainDir,
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                checkSessionParams,
                loginParams,
                scrapeParams,
                updateStatus,
                updateLastScrapeDate,
                dateRanges
            },
            {
                priority: getSourcePriority(source),
                context: {
                    source,
                    command: Command.SCRAPE,
                    shadowModeTask,
                    numberOfDaysToScrape: sumDaysInDateRanges(dateRanges),
                    dateRangesToProcess: dateRanges,
                    forceRunStart: false,
                    triggeredBy,
                    sessionPath: 'fake'
                }
            }
        );
    }

    async createGetSourceSideOrganizationsRun(
        sourceConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON
    ): Promise<Run<BaseContext, SourceSideOrganization[]>> {
        const sourceAccount = await this.getSourceAccount(sourceConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(sourceConfiguration.source, sourceAccount.sessionPath);
        return new Run<BaseContext, SourceSideOrganization[]>([new GetSourceSideOrganizationsJob(this.binaryProxy, commonParams)], {
            context: {source: sourceConfiguration.source, forceRunStart: false, command: Command.GET_SOURCE_SIDE_ORGANIZATIONS, triggeredBy}
        });
    }

    async createGetManualLoginDataRun(source: Source, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<Run<BaseContext, ManualLoginDetailsResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, undefined);
        return new Run<BaseContext, ManualLoginDetailsResult>([new GetManualLoginDataJob(this.binaryProxy, commonParams)], {
            context: {source, forceRunStart: true, command: Command.GET_MANUAL_LOGIN_DETAILS, triggeredBy}
        });
    }
    //TODO: make shadowModeTask required as we always pass it
    async createShadowModeLoginWithCredentialsRun(
        source: Source,
        scraperConfiguration: ScraperConfiguration,
        shadowModeTask?: ShadowModeTask
    ): Promise<Run<BaseContext, BinaryLoginResult>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);

        const loginParams: LoginParams = {
            ...(await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask)),
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        return new LoginWithCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams,
                scraperConfigurationManager: this.scraperConfigurationManager
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy: TriggeredBy.SHADOW_TASK,
                    shadowModeTask,
                    sessionPath: sourceAccount.sessionPath
                }
            }
        );
    }

    async createLoginWithCredentialsRun(source: Source, credentials: Credentials, sessionPath: string, triggeredBy: TriggeredBy): Promise<LoginWithCredentialsRun> {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials};

        return new LoginWithCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams,
                scraperConfigurationManager: this.scraperConfigurationManager
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath
                }
            }
        );
    }

    async createLoginWithCookiesRun(source: Source, triggeredBy: TriggeredBy, cookies: any[]): Promise<Run<LoginWithCookiesRunContext, ScraperConfiguration[]>> {
        return new LoginWithCookiesRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                scraperConfigurationManager: this.scraperConfigurationManager
            },
            {
                context: {
                    source,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath: buildSessionPath(this.mainDir, source),
                    command: Command.LOGIN, //TODO what is command for, is this not binary related?
                    cookies
                }
            }
        );
    }

    async createCheckCredentialsRun(source: Source, credentials: Credentials, sessionPath: string, triggeredBy: TriggeredBy) {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials};
        return new CheckCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                loginParams
            },
            {
                context: {
                    source,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath: commonParams.sessionPath!,
                    command: Command.LOGIN
                }
            }
        );
    }

    async createSetCredentialsRun(source: Source, credentials: Credentials, sessionPath: string, triggeredBy: TriggeredBy, sourceAccountId: string) {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials};
        return new SetCredentialsRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                scraperConfigurationManager: this.scraperConfigurationManager,
                loginParams
            },
            {
                context: {
                    source,
                    forceRunStart: true,
                    triggeredBy,
                    sessionPath: commonParams.sessionPath!,
                    command: Command.LOGIN
                },
                sourceAccountId
            }
        );
    }

    async getCommonParams(source: Source, sessionPath: string | undefined, shadowModeTask?: ShadowModeTask): Promise<CommonParams> {
        const sessionPath_ = shadowModeTask && sessionPath ? await copySessionFileForShadowRun(sessionPath) : sessionPath;

        const forcedExecPaths = shadowModeTask ? this.dependenciesManager.getExecPathsForShadowRun(shadowModeTask) : {};

        return {
            ...forcedExecPaths,
            source,
            sessionPath: sessionPath_,
            screenshotAndHtmlDumpPath: await this.storage.getScreenshotAndHtmlDumpDirPath(),
            featureFlags: getFeatureFlags()
        };
    }

    async getSourceAccount(scraperConfig: ScraperConfiguration): Promise<SourceAccount> {
        const sourceAccount = await this.storage.getSourceAccount(scraperConfig.sourceAccountId);
        if (!sourceAccount) {
            throw new Error('Linked source account not found.');
        }
        return sourceAccount;
    }
}
