import {errorType} from '../../../configurations/errorType';
import {BinaryProxy, LoginParams} from '../../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {LoginRunContext} from '../../../runs/jobs/context';
import {LoginWithCredentialsRun} from '../../../runs/LoginWithCredentialsRun';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {shadowModeLoginTestTask} from '../../utils/entries';
import {mockScraperServiceClient} from '../../utils/helpers';

const sessionPath = 'fakePath';
let sendLoginStateChangedEventSpy: jest.SpyInstance;

function getLoginWithCredentialsRunMock(context: Partial<LoginRunContext> = {}) {
    return new LoginWithCredentialsRun(
        {
            dependenciesManager: {} as DependenciesManager,
            binaryProxy: {} as BinaryProxy,
            s2Client: mockScraperServiceClient,
            loginParams: {sessionPath: sessionPath} as LoginParams,
            scraperConfigurationManager: {} as any
        },
        {
            context: {
                ...(context as LoginRunContext)
            }
        }
    );
}

describe('LoginWithCredentialsRun', () => {
    let run: LoginWithCredentialsRun;
    beforeEach(async () => {
        sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        run = getLoginWithCredentialsRunMock({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});
    });
    afterEach(jest.restoreAllMocks);

    it('onScheduled should send SCHEDULED event to S2', async () => {
        await run.onScheduled!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'SCHEDULED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onStart should send STARTED event to S2', async () => {
        await run.onStart!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onFail should send FAILED event to S2 with errorType', async () => {
        await run.onFail!({errorType: errorType.DEPENDENCIES_SYNC_ERROR});
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.DEPENDENCIES_SYNC_ERROR
        });
    });

    it('onFail should send FAILED event to S2 with default errorType if none provided', async () => {
        await run.onFail!({});
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.UNEXPECTED_ERROR
        });
    });

    it('onKill should send STOPPED event to S2', async () => {
        await run.onKill!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STOPPED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('onFinally should remove sessionPath if isShadowRun is true', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory');
        const run = getLoginWithCredentialsRunMock({shadowModeTask: shadowModeLoginTestTask, source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onFinally!();
        expect(removeFileOrDirectorySpy).toBeCalledWith(sessionPath, false);
    });

    it('onFinally should not remove sessionPath if isShadowRun is false', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory');

        await run.onFinally!();
        expect(removeFileOrDirectorySpy).not.toBeCalled();
    });
});
