import {errorType} from '../../../../configurations/errorType';
import {ScraperConfiguration, ScraperConfigurationStatus} from '../../../../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {CheckSessionResult} from '../../../../processes/types';
import {ScraperLibError} from '../../../../processes/types/errors';
import {LoginRunContext} from '../../../../runs/jobs/context';
import {SaveConfigurationJob} from '../../../../runs/jobs/SaveConfigurationJob';
import {TriggeredBy} from '../../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../../types';

const createMockConfigurations = (): ScraperConfiguration[] => [
    {
        id: 'config-1',
        source: Source.STEAM_SALES,
        sourceAccountId: 'account-1',
        status: ScraperConfigurationStatus.CONFIGURED,
        createdAt: new Date()
    },
    {
        id: 'config-2',
        source: Source.STEAM_WISHLISTS,
        sourceAccountId: 'account-1',
        status: ScraperConfigurationStatus.CONFIGURED,
        createdAt: new Date()
    }
];

const createMockContext = (overrides: Partial<LoginRunContext> = {}): LoginRunContext => ({
    source: Source.STEAM_SALES,
    sessionPath: '/path/to/session',
    operationId: 'test-operation-id',
    command: 'LOGIN' as any,
    isShadowRun: false,
    forceRunStart: false,
    triggeredBy: TriggeredBy.USER_VIA_ELECTRON,

    checkSessionResult: {
        id: 'test-account-id',
        hasScrapeBlockingIssues: false
    } as CheckSessionResult,

    ...overrides
});

const createMockScraperConfigurationManager = (mockConfigurations: ScraperConfiguration[] = createMockConfigurations()): jest.Mocked<ScraperConfigurationManager> =>
    ({
        addSourceAccountAndHandleRelatedConfigs: jest.fn().mockResolvedValue(mockConfigurations)
    } as any);

describe('SaveConfigurationJob', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('execute', () => {
        it('should successfully save configuration when session is valid', async () => {
            const mockConfigurations = createMockConfigurations();
            const mockScraperConfigurationManager = createMockScraperConfigurationManager(mockConfigurations);
            const job = new SaveConfigurationJob(mockScraperConfigurationManager);
            const context = createMockContext();

            const result = await job.execute(context);

            expect(mockScraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs).toHaveBeenCalledWith(Source.STEAM_SALES, {
                sessionPath: '/path/to/session',
                accountIdentifier: 'test-account-id'
            });
            expect(result).toEqual(mockConfigurations);
        });

        it('should successfully save configuration when session is valid and credentials are provided', async () => {
            const mockConfigurations = createMockConfigurations();
            const mockScraperConfigurationManager = createMockScraperConfigurationManager(mockConfigurations);
            const job = new SaveConfigurationJob(mockScraperConfigurationManager, {user: 'test-user', password: 'test-password'});
            const context = createMockContext();

            const result = await job.execute(context);

            expect(mockScraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs).toHaveBeenCalledWith(Source.STEAM_SALES, {
                sessionPath: '/path/to/session',
                accountIdentifier: 'test-account-id',
                cliParams: {user: 'test-user', password: 'test-password'}
            });
            expect(result).toEqual(mockConfigurations);
        });

        const invalidCheckSessionResultCases = [
            {
                description: 'when checkSessionResult is undefined',
                checkSessionResult: undefined
            },
            {
                description: 'when checkSessionResult is null',
                checkSessionResult: null
            },
            {
                description: 'when checkSessionResult.id is undefined',
                checkSessionResult: {
                    id: undefined,
                    hasScrapeBlockingIssues: false
                }
            },
            {
                description: 'when checkSessionResult.id is null',
                checkSessionResult: {
                    id: null,
                    hasScrapeBlockingIssues: false
                }
            },
            {
                description: 'when checkSessionResult.id is empty string',
                checkSessionResult: {
                    id: '',
                    hasScrapeBlockingIssues: false
                }
            }
        ];

        test.each(invalidCheckSessionResultCases)('should throw ScraperLibError $description', async ({checkSessionResult}) => {
            const mockScraperConfigurationManager = createMockScraperConfigurationManager();
            const job = new SaveConfigurationJob(mockScraperConfigurationManager);
            const context = createMockContext({
                checkSessionResult: checkSessionResult as any
            });

            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            await expect(job.execute(context)).rejects.toThrow(errorType.INTERNAL_SCRAPER_LIB_ERROR);
        });

        it('should include context in error when checkSessionResult is invalid', async () => {
            const mockScraperConfigurationManager = createMockScraperConfigurationManager();
            const job = new SaveConfigurationJob(mockScraperConfigurationManager);
            const context = createMockContext({
                checkSessionResult: undefined
            });

            const expectedError = new ScraperLibError(errorType.INTERNAL_SCRAPER_LIB_ERROR, 'error', {
                message: 'CheckSessionResult is not defined and a configuration is trying to be saved',
                context
            });

            await expect(job.execute(context)).rejects.toEqual(expectedError);
        });

        it('should propagate errors from addSourceAccountAndHandleRelatedConfigs', async () => {
            const error = new Error('Storage error');
            const mockScraperConfigurationManager = createMockScraperConfigurationManager();
            mockScraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs.mockRejectedValue(error);
            const job = new SaveConfigurationJob(mockScraperConfigurationManager);
            const context = createMockContext();

            await expect(job.execute(context)).rejects.toThrow('Storage error');
        });
    });

    it('should complete onSuccess without error', async () => {
        const mockScraperConfigurationManager = createMockScraperConfigurationManager();
        const job = new SaveConfigurationJob(mockScraperConfigurationManager);

        await expect(job.onSuccess(createMockContext())).resolves.toBeUndefined();
    });

    it('should complete onFail without error', async () => {
        const mockScraperConfigurationManager = createMockScraperConfigurationManager();
        const job = new SaveConfigurationJob(mockScraperConfigurationManager);
        const error = new Error('Test error');

        await expect(job.onFail(error, createMockContext())).resolves.toBeUndefined();
    });

    it('should complete kill without error', async () => {
        const mockScraperConfigurationManager = createMockScraperConfigurationManager();
        const job = new SaveConfigurationJob(mockScraperConfigurationManager);

        await expect(job.kill(createMockContext())).resolves.toBeUndefined();
    });

    it('should create instance with provided dependencies', () => {
        const mockScraperConfigurationManager = createMockScraperConfigurationManager();

        const job = new SaveConfigurationJob(mockScraperConfigurationManager);

        expect(job).toBeInstanceOf(SaveConfigurationJob);
    });
});
