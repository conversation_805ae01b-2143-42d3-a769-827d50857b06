import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../../../../configurations/SourceAccount';
import {LoginParams} from '../../../../dependencies/BinaryProxy';
import {UpdateSourceAccountJob, UpdateSourceAccountJobContext} from '../../../../runs/jobs/UpdateSourceAccountJob';
import {Source} from '../../../../types';

const createMockScraperConfigurationManager = (mockSourceAccount: SourceAccount): jest.Mocked<ScraperConfigurationManager> => {
    return {
        editSourceAccount: jest.fn().mockResolvedValue(mockSourceAccount),
        getSourceAccount: jest.fn().mockResolvedValue(mockSourceAccount)
    } as any;
};

describe('UpdateSourceAccountJob', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should update source account with correct data and return the updated account', async () => {
        const sourceAccountId = 'test-source-account-id';
        const mockLoginParams = {credentials: {user: 'test-user', password: 'test-password'}, source: Source.STEAM_SALES} as LoginParams;
        const context = {
            sessionPath: '/test/session/path',
            checkSessionResult: {id: 'test-account-identifier'}
        } as UpdateSourceAccountJobContext;

        const expectedUpdatedAccount = {
            id: sourceAccountId,
            cliParams: mockLoginParams.credentials,
            accountIdentifier: context.checkSessionResult!.id,
            sessionPath: context.sessionPath
        } as SourceAccount;

        const mockScraperConfigurationManager = createMockScraperConfigurationManager(expectedUpdatedAccount);
        const job = new UpdateSourceAccountJob(mockScraperConfigurationManager, mockLoginParams, sourceAccountId);

        const result = await job.execute(context);

        // Test behavior: verify the returned account has the correct updated data
        expect(result).toEqual(expectedUpdatedAccount);
        expect(result.id).toBe(sourceAccountId);
        expect(result.cliParams).toEqual(mockLoginParams.credentials);
        expect(result.accountIdentifier).toBe(context.checkSessionResult!.id);
        expect(result.sessionPath).toBe(context.sessionPath);

        // Verify the account can be retrieved with the updated data
        const retrievedAccount = await mockScraperConfigurationManager.getSourceAccount(sourceAccountId);
        expect(retrievedAccount).toEqual(expectedUpdatedAccount);
    });
});
