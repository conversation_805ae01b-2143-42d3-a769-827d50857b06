import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {LoginParams} from '../../../../dependencies/BinaryProxy';
import {UpdateSourceAccountJob, UpdateSourceAccountJobContext} from '../../../../runs/jobs/UpdateSourceAccountJob';
import {ElectronStorageImplementation} from '../../../../storage/ElectronStorageImplementation';
import {iScraperServiceClient} from '../../../../telemetry/scraperService/iScraperServiceClient';
import {Source} from '../../../../types';
import {newAdapterWithStorage, testSourceAccountWithParams} from '../../../utils/helpers';

const createMockScraperServiceClient = (): jest.Mocked<iScraperServiceClient> =>
    ({
        scheduleScraperStateChangedEvent: jest.fn(),
        scheduleLoginStateChangedEvent: jest.fn()
    } as unknown as jest.Mocked<iScraperServiceClient>);

describe('UpdateSourceAccountJob', () => {
    let scraperConfigurationManager: ScraperConfigurationManager;
    let storage: ElectronStorageImplementation;

    beforeEach(() => {
        const {adapter} = newAdapterWithStorage();
        storage = new ElectronStorageImplementation(adapter);
        const mockClient = createMockScraperServiceClient();
        scraperConfigurationManager = new ScraperConfigurationManager(storage, mockClient);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should update source account with correct data and return the updated account', async () => {
        const accountIdentifier = 'test-account-identifier';

        // Setup: Create an existing source account with the same accountIdentifier that will be used in context
        const initialSourceAccount = await storage.addSourceAccount({
            accountIdentifier,
            sessionPath: '/initial/session/path',
            cliParams: {
                initialParam: 'initial-value'
            }
        });

        const mockLoginParams = {credentials: {user: 'test-user', password: 'test-password'}, source: Source.STEAM_SALES} as LoginParams;
        const context = {
            sessionPath: '/test/session/path',
            checkSessionResult: {id: accountIdentifier}
        } as UpdateSourceAccountJobContext;

        const job = new UpdateSourceAccountJob(scraperConfigurationManager, mockLoginParams, initialSourceAccount.id);

        // Execute the job
        const result = await job.execute(context);

        // Test behavior: verify the returned account has the correct updated data
        expect(result.id).toBe(initialSourceAccount.id);
        expect(result.cliParams).toEqual({
            initialParam: 'initial-value',
            ...mockLoginParams.credentials
        });
        expect(result.accountIdentifier).toBe(accountIdentifier);
        expect(result.sessionPath).toBe(context.sessionPath);

        // Verify the account was actually updated in storage by retrieving it
        const retrievedAccount = await scraperConfigurationManager.getSourceAccount(initialSourceAccount.id);
        expect(retrievedAccount).toEqual(result);
        expect(retrievedAccount?.cliParams).toEqual({
            initialParam: 'initial-value',
            ...mockLoginParams.credentials
        });
        expect(retrievedAccount?.sessionPath).toBe(context.sessionPath);
    });
});
