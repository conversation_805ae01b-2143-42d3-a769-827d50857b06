import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../../../../configurations/SourceAccount';
import {LoginParams} from '../../../../dependencies/BinaryProxy';
import {UpdateSourceAccountJob, UpdateSourceAccountJobContext} from '../../../../runs/jobs/UpdateSourceAccountJob';
import {Source} from '../../../../types';

const createMockScraperConfigurationManager = (mockSourceAccount: SourceAccount): jest.Mocked<ScraperConfigurationManager> => {
    return {editSourceAccount: jest.fn().mockResolvedValue(mockSourceAccount)} as any;
};

describe('UpdateSourceAccountJob', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should call editSourceAccount with correct parameters and return the result', async () => {
        const mockSourceAccount = {id: 'mocked-source-account-id'} as SourceAccount;
        const mockScraperConfigurationManager = createMockScraperConfigurationManager(mockSourceAccount);
        const mockLoginParams = {credentials: {user: 'test-user', password: 'test-password'}, source: Source.STEAM_SALES} as LoginParams;
        const sourceAccountId = 'test-source-account-id';
        const job = new UpdateSourceAccountJob(mockScraperConfigurationManager, mockLoginParams, sourceAccountId);

        const context = {
            sessionPath: '/test/session/path',
            checkSessionResult: {id: 'test-account-identifier'}
        } as UpdateSourceAccountJobContext;

        const result = await job.execute(context);

        expect(mockScraperConfigurationManager.editSourceAccount).toHaveBeenCalledWith({
            id: sourceAccountId,
            cliParams: mockLoginParams.credentials,
            accountIdentifier: context.checkSessionResult!.id,
            sessionPath: context.sessionPath
        });
        expect(result).toEqual(mockSourceAccount);
    });
});
