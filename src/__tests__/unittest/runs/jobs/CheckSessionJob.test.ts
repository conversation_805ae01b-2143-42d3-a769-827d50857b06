import {errorType} from '../../../../configurations/errorType';
import {BinaryProxy, CheckSessionParams} from '../../../../dependencies/BinaryProxy';
import {ScraperLibError} from '../../../../processes/types/errors';
import {CheckSessionJob, CheckSessionJobContext} from '../../../../runs/jobs/CheckSessionJob';
import {JobStoppedByUserError} from '../../../../runs/JobStoppedByUserError';
import {Command} from '../../../../types';
import {nextEventLoopTick} from '../../../../utils/asyncUtils';
import {Deferred} from '../../../../utils/Deferred';

describe('CheckSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    const checkSessionParams = {some: 'value'} as any as CheckSessionParams;

    const createMockBinaryProxy = (result: Promise<any>): jest.Mocked<BinaryProxy> => {
        return {run: jest.fn().mockResolvedValue({result})} as any;
    };

    test('should set sessionValid to true and return result when session is valid', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const checkSessionResult = {hasScrapeBlockingIssues: false, id: 'test'};
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve(checkSessionResult));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);

        const result = await job.execute(context);

        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
        expect(result).toBe(checkSessionResult);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: true, checkSessionResult});
    });

    test('should call execution.kill and throw JobStoppedByUserError when killed during execution', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const executionResultDeferred = new Deferred<any>();
        const mockExecution = {result: executionResultDeferred.promise, kill: jest.fn()};
        const binaryProxyRunDeferred = new Deferred<typeof mockExecution>();
        const binaryProxyMock = {run: jest.fn().mockReturnValue(binaryProxyRunDeferred.promise)} as any;
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);

        const executePromise = job.execute(context);
        binaryProxyRunDeferred.resolve(mockExecution);
        await nextEventLoopTick();
        await job.kill(context);
        executionResultDeferred.reject(new Error('Session check failed'));

        expect(mockExecution.kill).toHaveBeenCalledWith(context.operationId);
        await expect(executePromise).rejects.toThrow(new JobStoppedByUserError('CHECK_SESSION_EXECUTE_KILL'));
        expect(context).toStrictEqual({operationId: 'fake'});
    });

    test('should set sessionValid to false when session is invalid', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.reject({hasScrapeBlockingIssues: true, id: 'test'}));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.SESSION_EXPIRED));
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: false});
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });

    test('should throw ScraperLibError when session has scrape blocking issues', async () => {
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve({hasScrapeBlockingIssues: true, id: 'test'}));
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.MISSING_PERMISSIONS));
    });
});
