import {DeleteSessionJob, DeleteSessionJobContext} from '../../../../runs/jobs/DeleteSessionJob';
import * as fileUtils from '../../../../utils/fileUtils';

describe('DeleteSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    test('should call removeFileOrDirectory with sessionPath', async () => {
        const mockedRemoveFileOrDirectory = jest.spyOn(fileUtils, 'removeFileOrDirectory');
        mockedRemoveFileOrDirectory.mockResolvedValue();
        const context = {sessionPath: '/path/to/session'} as DeleteSessionJobContext;
        const job = new DeleteSessionJob();

        await job.execute(context);

        expect(mockedRemoveFileOrDirectory).toHaveBeenCalledWith('/path/to/session');
    });
});
