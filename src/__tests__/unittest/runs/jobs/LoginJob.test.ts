import {errorType} from '../../../../configurations/errorType';
import {BinaryProxy, LoginParams} from '../../../../dependencies/BinaryProxy';
import {BinaryLoginResult} from '../../../../processes/types';
import {ScraperLibError} from '../../../../processes/types/errors';
import {LoginJob, LoginJobContext} from '../../../../runs/jobs/LoginJob';
import {Command, Source} from '../../../../types';
import {nextEventLoopTick} from '../../../../utils/asyncUtils';
import {Deferred} from '../../../../utils/Deferred';
import {asyncVoidFunc, getFreshDependenciesManager, scraperLibMainDirectory} from '../../../utils/helpers';

describe('LoginJob', () => {
    afterEach(jest.restoreAllMocks);

    const loginParams = {
        credentials: {username: 'test', password: 'test'},
        source: Source.EPIC_SALES
    } as any as LoginParams;

    const createMockBinaryProxy = (result: Promise<any>): jest.Mocked<BinaryProxy> => {
        return {run: jest.fn().mockResolvedValue({result})} as any;
    };

    test('should login with credendials successfully and set sessionValid to true', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const loginResult: BinaryLoginResult = {id: 'user123'};
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve(loginResult));
        const job = new LoginJob(binaryProxyMock, loginParams);

        const result = await job.execute(context);

        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.LOGIN, loginParams, context.operationId);
        expect(result).toBe(loginResult);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: true, checkSessionResult: loginResult});
    });

    test('should throw "Session expired" error when credentials are missing', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve({}));
        const job = new LoginJob(binaryProxyMock, {} as LoginParams);

        await expect(job.execute(context)).rejects.toThrow('Session expired');
        expect(binaryProxyMock.run).not.toHaveBeenCalled();
    });

    test('should return correct value from shouldRun based on sessionValid', () => {
        const job = new LoginJob({} as BinaryProxy, {} as LoginParams);

        expect(job.shouldRun({sessionValid: undefined} as LoginJobContext, [])).toBe(true);
        expect(job.shouldRun({sessionValid: false} as LoginJobContext, [])).toBe(true);
        expect(job.shouldRun({sessionValid: true} as LoginJobContext, [])).toBe(false);
    });

    test('should call execution.kill when killed during execution', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const executionResultDeferred = new Deferred<BinaryLoginResult>();
        const mockExecution = {result: executionResultDeferred.promise, kill: jest.fn()};
        const binaryProxyRunDeferred = new Deferred<typeof mockExecution>();
        const binaryProxyMock = {run: jest.fn().mockReturnValue(binaryProxyRunDeferred.promise)} as any;
        const job = new LoginJob(binaryProxyMock, loginParams);

        const executePromise = job.execute(context);
        binaryProxyRunDeferred.resolve(mockExecution);
        await nextEventLoopTick();
        await job.kill(context);
        executionResultDeferred.resolve({} as BinaryLoginResult);

        expect(mockExecution.kill).toHaveBeenCalledWith(context.operationId);
        await executePromise;
    });

    describe('error handling', () => {
        const createBinaryProxyWithMockScript = (scriptName: string) => {
            const manager = getFreshDependenciesManager();
            jest.spyOn(manager, 'downloadDependency').mockImplementation(asyncVoidFunc);
            jest.spyOn(manager, 'syncDependencies').mockImplementation(asyncVoidFunc);
            jest.spyOn(manager, 'getDependencyExecPaths').mockResolvedValue({
                chromiumExecPath: 'chromium.exe',
                scrapersExecPath: `./src/__tests__/integration/mocks/${scriptName}`
            });
            return new BinaryProxy(scraperLibMainDirectory, manager);
        };

        test('should propagate SESSION_EXPIRED error from binary to LoginJob', async () => {
            const context = {operationId: 'fake'} as LoginJobContext;
            const mockBinaryProxy = createBinaryProxyWithMockScript('binary_mock_session_expired.sh');
            const job = new LoginJob(mockBinaryProxy, loginParams);

            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            await expect(job.execute(context)).rejects.toMatchObject({
                errorType: errorType.SESSION_EXPIRED,
                logLevel: 3
            });
        });

        test('should propagate INCORRECT_CREDENTIALS error from binary to LoginJob', async () => {
            const context = {operationId: 'fake'} as LoginJobContext;
            const mockBinaryProxy = createBinaryProxyWithMockScript('binary_mock_incorrect_credentials.sh');
            const job = new LoginJob(mockBinaryProxy, loginParams);

            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            await expect(job.execute(context)).rejects.toMatchObject({
                errorType: errorType.INCORRECT_CREDENTIALS,
                logLevel: 3
            });
        });

        test('should propagate MISSING_PERMISSIONS error with additional data from binary to LoginJob', async () => {
            const context = {operationId: 'fake'} as LoginJobContext;
            const mockBinaryProxy = createBinaryProxyWithMockScript('binary_mock_missing_permissions.sh');
            const job = new LoginJob(mockBinaryProxy, loginParams);

            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            const error = await job.execute(context).catch((e) => e);
            expect(error).toMatchObject({
                errorType: errorType.MISSING_PERMISSIONS,
                logLevel: 3
            });
            expect(error.additionalErrorData.fullErrorAsJson.data.additionalErrorData).toMatchObject({
                organizationsWithMissingPermissions: [
                    {name: 'test', id: 'test'},
                    {name: 'test2', id: 'test2'}
                ]
            });
        });

        test('should propagate UNEXPECTED_ERROR from binary to LoginJob', async () => {
            const context = {operationId: 'fake'} as LoginJobContext;
            const mockBinaryProxy = createBinaryProxyWithMockScript('binary_mock_unexpected_error.sh');
            const job = new LoginJob(mockBinaryProxy, loginParams);

            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            await expect(job.execute(context)).rejects.toMatchObject({
                errorType: errorType.UNEXPECTED_ERROR,
                logLevel: 3
            });
        });
    });
});
