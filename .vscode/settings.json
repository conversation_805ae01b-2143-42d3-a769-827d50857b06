{
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[markdown]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "jest.runMode": {
    "type": "on-demand",
  },
  "jest.jestCommandLine": "node_modules/.bin/jest --testTimeout=100000000",
  "diffEditor.ignoreTrimWhitespace": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "files.eol": "\n",
  "testExplorer.useNativeTesting": true,
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.implementationsCodeLens.enabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.referencesCodeLens.enabled": true,
  "typescript.referencesCodeLens.showOnAllFunctions": false,
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false,
  "workbench.colorCustomizations": {
    "activityBar.activeBackground": "#ffffff",
    "activityBar.background": "#ffffff",
    "activityBar.foreground": "#15202b",
    "activityBar.inactiveForeground": "#15202b99",
    "activityBarBadge.background": "#df9f9f",
    "activityBarBadge.foreground": "#15202b",
    "commandCenter.border": "#15202b99",
    "sash.hoverBorder": "#ffffff",
    "statusBar.background": "#ffffff",
    "statusBar.foreground": "#15202b",
    "statusBarItem.hoverBackground": "#e6e6e6",
    "statusBarItem.remoteBackground": "#ffffff",
    "statusBarItem.remoteForeground": "#15202b",
    "titleBar.activeBackground": "#ffffff",
    "titleBar.activeForeground": "#15202b",
    "titleBar.inactiveBackground": "#ffffff99",
    "titleBar.inactiveForeground": "#15202b99"
  },
  "peacock.color": "#ffffff"
}
